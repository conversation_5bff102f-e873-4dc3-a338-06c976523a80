version: "3.7"

services:
  web:
    image: "clickeedevweb:latest"
    deploy:
      replicas: 1
      placement:
        constraints: [ node.hostname==k8sbiai1 ]
      restart_policy:
        condition: any
    environment:
      PORT: 80
      NODE_ENV: "production"
      SERVICE_80_NAME: "clickeedevweb"
      SERVICE_NAME: "clickeedevweb"
      SERVICE_TAGS: "clickeedevweb"
      GA_ID: "G-WH2LW8XKZG"
    ports:
      - target: 80
        published: 8011
        mode: host
