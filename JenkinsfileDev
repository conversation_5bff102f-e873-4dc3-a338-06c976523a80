pipeline {
    agent any
    stages {
        stage('Init') {
            steps {
                echo 'Testing..'
                telegramSend(message: 'Building job: $PROJECT_NAME ... - Link: $BUILD_URL', chatId: -4220317008)
            }
        }
        stage ('Deployments') {
            steps {
                echo 'Deploying to Production environment...'
                echo 'Copy project over SSH...'
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: 'thinklabs20',
                        transfers:
                            [sshTransfer(
                                cleanRemote: false,
                                excludes: '',
                                execCommand: "docker build -t clickeedevweb ./thinklabsdev/clickeedevwebCI/ \
                                    && docker service rm clickeedev_web || true \
                                    && docker stack deploy -c ./thinklabsdev/clickeedevwebCI/docker-compose-dev.yml clickeedev \
                                    && rm -rf ./thinklabsdev/clickeedevwebCIB \
                                    && mv ./thinklabsdev/clickeedevwebCI/ ./thinklabsdev/clickeedevwebCIB",
                                execTimeout: 60000000,
                                flatten: false,
                                makeEmptyDirs: false,
                                noDefaultExcludes: false,
                                patternSeparator: '[, ]+',
                                remoteDirectory: './thinklabsdev/clickeedevwebCI',
                                remoteDirectorySDF: false,
                                removePrefix: '',
                                sourceFiles: '*, src/, server/, webpack/'
                            )],
                        usePromotionTimestamp: false,
                        useWorkspaceInPromotion: false,
                        verbose: false
                    )
                ])
                telegramSend(message: 'Build - $PROJECT_NAME – # $BUILD_NUMBER – STATUS: $BUILD_STATUS!', chatId: -4220317008)
            }
        }
    }
}
