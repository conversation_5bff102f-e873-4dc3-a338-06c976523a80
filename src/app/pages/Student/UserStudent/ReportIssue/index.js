import { Input, Modal } from "antd";
import { useTranslation } from "react-i18next";
import './ReportIssue.scss'
import UPLOAD from "@src/assets/icons/upload-04.svg";
import DELETE_FILES from "@src/assets/icons/delete-files.svg";
import DELETE_IMAGES from "@src/assets/icons/delete-images.svg";
import LINK_SIMPLE from "@src/assets/icons/LinkSimple.svg";
import THANKYOU_FEEDBACK from "@src/asset/image/thankyou-feedback.svg";

import { useRef, useState } from "react";
import { toast } from "@src/app/component/ToastProvider";
import { uploadFile } from "@src/app/services/File";
import { setReportIssue } from "@src/app/services/ErrorReports";
import Close from "@src/app/component/SvgIcons/Close";

const ReportIssue = ({ isModalVisible, setIsModalVisible, imagePreviews, setImagePreviews, videoPreview, setVideoPreview }) => {
  const { t } = useTranslation();

  const filesInputRef = useRef(null);

  const [isUploading, setIsUploading] = useState(false);
  const [images, setImages] = useState([])
  const [video, setVideo] = useState(null)
  const [otherText, setOtherText] = useState("");
  const [selectedValue, setSelectedValue] = useState("");

  const [link, setLink] = useState("");
  const [error, setError] = useState("");
  const [textEmpty, setTextEmpty] = useState(false)

  const [showThankYou, setShowThankYou] = useState(false);

  const imageIds = images?.map(image => image._id);

  const handleOtherTextChange = (event) => {
    setTextEmpty(false);
    setOtherText(event.target.value);
  };

  const handleBlurDescription = () => {
    if (!otherText?.trim()) {
      setTextEmpty(true);
    } else {
      setTextEmpty(false);
    }
  }

  const isValidUrl = (url) => {
    const trimmedUrl = url.trim();
    // After trimming, a valid URL should not contain any spaces.
    if (/\s/.test(trimmedUrl)) {
      return false;
    }
    try {
      const urlObject = new URL(trimmedUrl);
      // A valid URL for our purposes should have a protocol and a hostname.
      // This prevents URLs like "http://" or "mailto:<EMAIL>"
      return !!(urlObject.protocol && urlObject.hostname);
    } catch {
      return false;
    }
  };

  const handleBlur = () => {
    if (link && !isValidUrl(link)) {
      setError(`${t('PLEASE_ENTER_LINK_ERR')}`);
    } else {
      setError("");
    }
  };

  const handleChangeLink = (e) => {
    setError('');
    setLink(e.target.value);
  }

  const handleChangeImpactLv = (e) => {
    setSelectedValue(e.target.value);
  }

  const handleFilesClick = () => {
    filesInputRef.current.click();
  };


  const handleFilesChange = async (e) => {
    const files = Array.from(e.target.files);
    if (!files.length) return;

    const MAX_SIZE_MB = 20;
    const imageTypes = ["image/png", "image/jpeg"];
    const videoTypes = ["video/mp4"];

    // Kiểm tra nếu có file vượt quá 20MB
    const oversizedFile = files.find(file => file.size / 1024 / 1024 > MAX_SIZE_MB);
    if (oversizedFile) {
      toast.warning({ description: t(`FILE_TOO_LARGE`) });
      return;
    }

    // Phân loại file
    const imageFiles = files.filter(file => imageTypes.includes(file.type));
    const videoFiles = files.filter(file => videoTypes.includes(file.type));
    const invalidFiles = files.filter(file => !imageTypes.includes(file.type) && !videoTypes.includes(file.type));

    if (invalidFiles.length > 0) {
      toast.warning({ description: t("INVALID_FILE_FORMAT") });
      return;
    }

    setIsUploading(true);

    // Xử lý ảnh
    if (imageFiles.length > 0) {
      if (images.length + imageFiles.length > 5) {
        toast.warning({ description: t("IMAGES_NUMBER_LIMIT") });
        setIsUploading(false);
        return;
      }

      const uploadedImageDetails = [];
      const newImagePreviews = [];

      for (const file of imageFiles) {
        try {
          const apiResponse = await uploadFile(file, { folder: "image" });
          if (apiResponse) {
            const sizeInBytes = file.size;
            const sizeInMB = sizeInBytes / 1024 / 1024;
            uploadedImageDetails.push({
              _id: apiResponse._id,
              name: apiResponse.name,
              sizeInBytes,
              sizeFormatted: sizeInMB >= 1 ? `${sizeInMB.toFixed(2)} MB` : `${Math.round(sizeInBytes / 1024)} KB`,
            });
            newImagePreviews.push({ url: URL.createObjectURL(file) });
          }
        } catch (error) {
          console.error("Error uploading image:", file.name, error);
          toast.error({ description: t("UPLOAD_IMAGE_FAILED") });
        }
      }

      setImages(prev => [...prev, ...uploadedImageDetails]);
      setImagePreviews(prev => [...prev, ...newImagePreviews]);
    }

    // Xử lý video
    if (videoFiles.length > 0) {
      if (videoPreview) {
        URL.revokeObjectURL(videoPreview);
      }

      const videoFile = videoFiles[0]; // Chỉ lấy video đầu tiên
      try {
        const apiResponse = await uploadFile(videoFile, { folder: "video" });
        if (apiResponse) {
          setVideo({
            _id: apiResponse._id,
            name: videoFile.name,
            size: +(videoFile.size / 1024 / 1024).toFixed(2),
          });
          setVideoPreview(URL.createObjectURL(videoFile));
        }
      } catch (error) {
        console.error("Error uploading video:", videoFile.name, error);
        toast.error({ description: t("UPLOAD_VIDEO_FAILED") });
      }
    }

    setIsUploading(false);
  };


  const handlePaste = async (e) => {
    const clipboardItems = e.clipboardData?.items;
    if (!clipboardItems) return;

    const imageItems = Array.from(clipboardItems).filter(item => item.type.startsWith("image/"));

    if (imageItems.length > 0) {
      e.preventDefault(); // ngăn paste văn bản
      if (images.length + imageItems.length > 5) {
        toast.warning({ description: t("IMAGES_NUMBER_LIMIT") });
        return;
      }

      setIsUploading(true); // Bắt đầu tải lên
      const uploadedImageDetails = [];
      const newImagePreviews = [];

      for (const item of imageItems) {
        const file = item.getAsFile();
        if (file) {
          try {
            const apiResponse = await uploadFile(file, { folder: "image" }); // Gọi API upload
            if (apiResponse) {
              const sizeInBytes = file.size;
              const sizeInMB = sizeInBytes / 1024 / 1024;
              const uploadedData = {
                _id: apiResponse._id,
                name: apiResponse.name || "pasted-image.png", // Tên mặc định cho ảnh dán
                sizeInBytes: sizeInBytes,
                sizeFormatted: sizeInMB >= 1
                  ? `${sizeInMB.toFixed(2)} MB`
                  : `${Math.round(sizeInBytes / 1024)} KB`,
                url: apiResponse.url || URL.createObjectURL(file),
              };
              uploadedImageDetails.push(uploadedData);
              newImagePreviews.push({ url: URL.createObjectURL(file) });
            }
          } catch (error) {
            console.error("Error uploading pasted image:", error);
            toast.error({ description: t("UPLOAD_IMAGE_FAILED") });
          }
        }
      }

      setImages(prev => [...prev, ...uploadedImageDetails]);
      setImagePreviews(prev => [...prev, ...newImagePreviews]);
      setIsUploading(false); // Kết thúc tải lên
    } else {
      // Nếu không có ảnh trong clipboard, ngăn chặn việc dán và cảnh báo
      e.preventDefault();
      toast.warning({ description: t("ONLY_UPLOADED_IMAGES") });
    }
  };

  const handleKeyDown = (e) => {
    // Cho phép tổ hợp Ctrl/Cmd/Alt/Shift để paste vẫn hoạt động
    if (e.ctrlKey) return;

    if (['Tab', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(e.key)) {
      return;
    }

    // Ngăn mọi phím khác (chữ, số, backspace, delete, space...)
    e.preventDefault();
    if (/^[\w\d\s\p{P}\p{S}]$/u.test(e.key)) {
      toast.warning({ description: t("ONLY_UPLOADED_IMAGES") });
    }
  };

  const handleDeleteVideoPreView = () => {
    setVideo(null);
    setVideoPreview(null)
    URL.revokeObjectURL(videoPreview);
  }

  const handleDeleteImagePreview = (index) => {
    const image = images[index];
    if (image?.url) {
      URL.revokeObjectURL(image.url);
    }

    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    if (!otherText?.trim()) {
      console.error("Description is required");
      return;
    }
    const data = {
      description: otherText?.trim(),
      ...(imageIds?.length > 0 && { imageIds }),
      ...(video?._id && { videoId: video._id }),
      ...(link?.trim() && { errorUrl: link }),
      ...(selectedValue && { impactLevel: selectedValue }),
    };
    try {
      const response = await setReportIssue(data);
      if (response) {
        setShowThankYou(true);
      }
    } catch (error) {
      console.log(error);
    }
  }

  const handleCloseThankYou = () => {
    setShowThankYou(false);
    setIsModalVisible();
  };

  return (
    <>
      <Modal
        title={t("BUG_REPORT")}
        open={isModalVisible}
        onCancel={setIsModalVisible}
        footer={
          <div className="modal-content__footer">
            <div className={`${(otherText?.trim().length > 0 && error.length === 0 && !isUploading) ? 'modal-content__footer__button' : 'modal-content__footer__button-error'}`}
              onClick={handleSubmit}
            >
              {t("SUBMIT_BUG_REPORT")}
            </div>
          </div>
        }
        className="modal-report-issue"
      >
        <div className="modal-content">
          <div className="modal-content__boder-top"></div>
          <div className="modal-content__body">
            <div className="modal-content__body__description">
              <span className="modal-content__body__description__title">
                1. {t('ISSUE_DESCRIPTION')}
              </span>
              <div className="flex flex-col gap-1">
                <textarea
                  rows={3}
                  placeholder={t("ENTER_YOUR_ISSUE")}
                  className={`textarea-issue ${!textEmpty ? '' : 'textarea-issue-error'}`}
                  onChange={handleOtherTextChange}
                  onBlur={handleBlurDescription}
                  maxLength={200}
                />
                <div className="modal-content__body__description__textarea">
                  {textEmpty && (
                    <div className="select-user-hear-about-us__body__other__text-input__error">
                      <div className="select-user-hear-about-us__body__other__text-input__error__text">
                        {t('PLEASE_ENTER_YOUR_INFORMATION')}
                      </div>
                    </div>
                  )}
                  <div className="modal-content__body__description__textarea__count">
                    {otherText.length}/200
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-content__body__description">
              <div className="modal-content__body__description__header">
                <span className="modal-content__body__description__title">
                  2. {t('ATTACHMENT')}
                </span>
                <span className="modal-content__body__description__header__note">
                  {t("NOTE_UPLOAD_IMAGES")}
                </span>
                {videoPreview && (
                  <div className="video-preview-container">
                    <div className="video-preview-border"
                      onClick={() => window.open(videoPreview, '_blank')}
                    >
                      <img src={LINK_SIMPLE} alt="link" className="link-simple" />
                      <span className="video-preview-name">
                        {video?.name} <span className="video-preview-size">({video?.size} MB)</span>
                      </span>
                    </div>
                    <img src={DELETE_FILES} alt="delete" className="delete-files" onClick={handleDeleteVideoPreView} />
                  </div>
                )}
                {imagePreviews.length > 0 && (
                  <div className="image-preview-container">
                    {imagePreviews?.map((preview, index) => {
                      return (
                        <div key={index} className="image-preview-border">
                          <img src={preview.url} alt="preview" className="image-preview"
                            onClick={() => window.open(preview.url, '_blank')}
                          />
                          <img
                            src={DELETE_IMAGES}
                            alt="delete"
                            className="delete-images"
                            onClick={() => handleDeleteImagePreview(index)}
                          />
                        </div>
                      )
                    })}
                  </div>
                )}
              </div>
              <Input
                className="custom-input"
                size="large"
                placeholder={t("UPLOAD_IMAGES_VIDEO")}
                onPaste={handlePaste}
                onKeyDown={handleKeyDown}
                suffix={
                  <span style={{ display: "flex", alignItems: "center", gap: "4px", cursor: "pointer" }}
                    onClick={handleFilesClick}
                  >
                    <img src={UPLOAD} alt="upload" />
                    <span style={{ color: "#36a6ff", fontSize: "12px", lineHeight: "16px", fontWeight: "500" }}>{t("UPLOAD_FILE")}</span>
                    <input
                      type="file"
                      accept=".png,.jpg,.jpeg,.mp4"
                      ref={filesInputRef}
                      style={{ display: 'none' }}
                      onChange={handleFilesChange}
                      multiple
                    />
                  </span>
                }
              />
            </div>
            <div className="modal-content__body__description">
              <span className="modal-content__body__description__title">
                3. {t("LINK_WITH_ERROR")}
              </span>
              <div className="flex flex-col gap-1">
                <Input
                  size="large"
                  placeholder={t("ENTER_YOUR_LINK_ERROR")}
                  value={link}
                  onChange={handleChangeLink}
                  onBlur={handleBlur}
                  status={error ? "error" : ""}
                />
                {error && (
                  <div className="select-user-hear-about-us__body__other__text-input__error">
                    <div className="select-user-hear-about-us__body__other__text-input__error__text">
                      {error}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-content__body__description">
              <span className="modal-content__body__description__title">
                4. {t("IMPACT_LEVEL")}
              </span>
              <div className="modal-content__body__description__item">
                <label className="modal-content__body__description__item__label">
                  <input type="radio" name="source" value="low"
                    className="modal-content__body__description__item__label__input"
                    checked={selectedValue === "low"}
                    onChange={handleChangeImpactLv}
                  />
                  <span>{t("MINOR_ERR")}</span>
                </label>

                <label className="modal-content__body__description__item__label">
                  <input type="radio" name="source" value="medium"
                    className="modal-content__body__description__item__label__input"
                    checked={selectedValue === "medium"}
                    onChange={handleChangeImpactLv}
                  />
                  <span>{t("MODERATE_ERR")}</span>
                </label>

                <label className="modal-content__body__description__item__label">
                  <input type="radio" name="source" value="high"
                    className="modal-content__body__description__item__label__input"
                    checked={selectedValue === "severe"}
                    onChange={handleChangeImpactLv}
                  />
                  <span>{t("SEVERE_ERR")}</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      <Modal
        open={showThankYou}
        footer={null}
        closable={true}
        onCancel={handleCloseThankYou}
        closeIcon={<Close />}
        width={542}
        className="feedback-thank-you-modal"
        centered
      >
        <div className="feedback-thank-you p-0">
          <div className="feedback-thank-you__image">
            <img src={THANKYOU_FEEDBACK} alt="Thank you" />
          </div>
          <div className="flex flex-col gap-2">
            <div className="feedback-thank-you__text">
              {t("THANK_YOU_FOR_YOUR_ERR_RRPORT")}
            </div>
            <div className="feedback-thank-you__text">
              {t("THANK_YOU_FOR_YOUR_ERR_RRPORT_CONTENT")}
            </div>
          </div>
        </div>
      </Modal>
    </>

  );
}

export default ReportIssue;