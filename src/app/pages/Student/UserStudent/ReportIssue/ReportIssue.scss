.ant-modal-wrap:has(.modal-report-issue) {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  overflow: hidden;

  .ant-modal {
    padding: 0;
    margin: 0;
    margin-right: 32px;
    margin-top: -70px;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;


    .ant-modal-content {
      border-radius: 16px;
      padding: 0;
      display: flex;
      flex-direction: column;

      .ant-modal-close-x {
        color: black;
        font-size: 16px;
        font-weight: 600;
      }

      .ant-modal-header {
        margin: 0;
        padding: 16px 24px;
        // border-bottom: 1px solid #e0e0e0;

        .ant-modal-title {
          font-size: 22px;
          list-style: 30px;
          font-weight: 600;
          color: black;
        }
      }

      .ant-modal-body {
        max-height: 65vh;
        overflow-y: auto;
        flex-grow: 1;
      }

      /* Ẩn scrollbar arrow buttons trên WebKit browsers */
      .ant-modal-body::-webkit-scrollbar-button {
        display: none;
      }

      /* <PERSON><PERSON> chọn: tuỳ chỉnh thanh cuộn */
      .ant-modal-body::-webkit-scrollbar {
        width: 6px;
      }

      .ant-modal-body::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border-radius: 20px;
      }

      .ant-modal-body::-webkit-scrollbar-track {
        background-color: transparent;
      }

      .ant-modal-footer {
        margin: 0;
        padding: 12px 24px 20px 24px;

      }
    }
  }
}

.modal-content {
  padding: 0 24px 12px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  &__boder-top {
    height: 0.5px;
    width: 100%;
    background-color: #e0e0e0;

  }

  &__body {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &__description {
      display: flex;
      flex-direction: column;
      gap: 16px;

      &__textarea {
        display: flex;
        // justify-content: space-between;
        width: 100%;

        &__count {
          margin-left: auto;
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          color: #b3b3b3;
        }
      }

      &__header {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &__note {
          font-size: 12px;
          font-weight: 400;
          line-height: 16px;
          color: #b3b3b3;
        }

        .video-preview-container {
          display: flex;
          flex-direction: row;
          gap: 4px;

          .delete-files {
            cursor: pointer;
          }

          .delete-files:hover {
            opacity: 0.7;
          }

          .video-preview-border {
            display: flex;
            gap: 4px;
            cursor: pointer;

            .link-simple {
              cursor: pointer;
            }

            .video-preview-name {
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              color: #36a6ff;
              cursor: pointer;
            }

            .video-preview-size {
              color: #000;
            }
          }
        }

        .image-preview-container {
          display: flex;
          gap: 4px;

          .image-preview-border {
            position: relative;
            max-height: 72px;

            .image-preview {
              width: 70px;
              height: 70px;
              object-fit: cover;
              border-radius: 8px;
              border: 1px solid #dbdbdb;
              cursor: pointer;
            }

            .delete-images {
              position: absolute;
              top: 0;
              right: 0;
              cursor: pointer;
              padding: 4px;
            }

            .delete-images:hover {
              opacity: 0.7;
            }
          }
        }
      }

      &__title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        color: black;
      }

      .textarea-issue-error {
        border: 1px solid red !important;
      }

      .textarea-issue {
        // width: 368px;
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #dbdbdb;
        resize: none;
        font-size: 16px;
        font-family: sans-serif;
        line-height: 24px;
        font-weight: 400;
        outline: none;
      }

      .textarea-issue::placeholder {
        color: #b3b3b3;
        font-size: 15px;
        line-height: 22px;
        font-weight: 400;
        font-family: sans-serif;
      }

      /* Ẩn scrollbar arrow buttons trên WebKit browsers */
      .textarea-issue::-webkit-scrollbar-button {
        display: none;
      }

      /* Tuỳ chọn: tuỳ chỉnh thanh cuộn */
      .textarea-issue::-webkit-scrollbar {
        width: 6px;
      }

      .textarea-issue::-webkit-scrollbar-thumb {
        background-color: #ccc;
        border-radius: 20px;
      }

      .textarea-issue::-webkit-scrollbar-track {
        background-color: transparent;
      }

      .ant-input-affix-wrapper {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid #dbdbdb;
        font-size: 14px;
        font-family: sans-serif;
        line-height: 20px;
        font-weight: 400;
        outline: none;
      }

      .ant-input {
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid #dbdbdb;
        font-size: 14px;
        font-family: sans-serif;
        line-height: 20px;
        font-weight: 400;
        outline: none;
      }

      .ant-input::placeholder {
        color: #b3b3b3;
      }

      /* SCSS hoặc CSS thường */
      .custom-input.ant-input-affix-wrapper:focus,
      .custom-input.ant-input-affix-wrapper-focused {
        box-shadow: none !important;
        border-color: #dbdbdb !important;
        /* hoặc bất kỳ màu nào bạn muốn */
      }

      .ant-input:focus,
      .ant-input-focused {
        box-shadow: none !important;
        border-color: #dbdbdb !important;
        /* hoặc bất kỳ màu nào bạn muốn */
      }

      &__item {
        display: flex;
        flex-direction: column;
        gap: 18px;

        &__label {
          display: flex;
          align-items: center;
          gap: 11px;

          &__input {
            width: 20px;
            height: 20px;
            margin: 0;
            padding: 0;
            accent-color: #3a18ce;
            cursor: pointer;
          }

          span {
            font-size: 16px;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }

    }
  }

  &__footer {
    display: flex;
    justify-content: center;

    &__button {
      padding: 8px 24px;
      border-radius: 12px;
      background-color: #26d06d;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      border: none;
      cursor: pointer;
    }

    &__button:hover {
      opacity: 0.9;
    }

    &__button-error {
      padding: 8px 24px;
      border-radius: 12px;
      background-color: #b3b3b3;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      border: none;
      cursor: no-drop;
    }
  }
}