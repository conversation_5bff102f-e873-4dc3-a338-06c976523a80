import React, { useEffect, useState } from "react";
import { Card, Col, Row, Tag, Image, Button, Space, Descriptions, Typography, Switch, Input, Divider } from "antd";
import { useTranslation } from "react-i18next";
import { CheckOutlined, CloseOutlined, PlayCircleOutlined, UserOutlined, LinkOutlined, CalendarOutlined, FileImageOutlined, VideoCameraOutlined } from "@ant-design/icons";

import AntModal from "@src/app/component/AntModal";
import Loading from "@src/app/component/Loading";
import AntButton from "@src/app/component/AntButton";

import { formatDate } from "@src/common/functionCommons";
import { BUTTON } from "@constant";
import { getErrorReportDetail, updateErrorReport } from "@services/ErrorReport";
import { toast } from "@src/app/component/ToastProvider";
import { API } from "@api";

import "./ErrorReportDetail.scss";

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

function ErrorReportDetail({ open, errorReport, onClose }) {
  const { t } = useTranslation();
  const [detailData, setDetailData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [mediaErrors, setMediaErrors] = useState({});

  useEffect(() => {
    if (open && errorReport?._id) {
      fetchErrorReportDetail();
    }
  }, [open, errorReport]);

  const fetchErrorReportDetail = async () => {
    setIsLoading(true);
    try {
      const response = await getErrorReportDetail(errorReport._id);
      if (response) {
        setDetailData(response);
      }
    } catch (error) {
      toast.error(t("FETCH_ERROR_REPORT_DETAIL_ERROR"));
    }
    setIsLoading(false);
  };



  const handleImageError = (imageId, error) => {
    console.error(`Failed to load image ${imageId}:`, error);
    setMediaErrors(prev => ({ ...prev, [`image_${imageId}`]: true }));
  };

  const handleVideoError = (videoId, error) => {
    console.error(`Failed to load video ${videoId}:`, error);
    setMediaErrors(prev => ({ ...prev, [`video_${videoId}`]: true }));
  };

  const renderMediaContent = () => {
    if (!detailData) return null;

    const { imageIds = [], videoId } = detailData;

    return (
      <div className="media-content">
        {imageIds.length > 0 && (
          <div className="images-section">
            <div className="media-header">
              <FileImageOutlined className="media-icon" />
              <Title level={5} className="media-title">
                {t("ATTACHED_IMAGES")} ({imageIds.length})
              </Title>
            </div>
            <div className="images-gallery">
              <Image.PreviewGroup>
                {imageIds.map((image, index) => {
                  // Handle different image data structures
                  const imageFileId = image.imageFileId || image._id || image;
                  const imageSrc = typeof imageFileId === 'string'
                    ? API.STREAM_ID.format(imageFileId)
                    : API.STREAM_ID.format(imageFileId?._id || imageFileId);

                  const hasError = mediaErrors[`image_${imageFileId}`];

                  return (
                    <div key={image._id || index} className="image-wrapper">
                      {hasError ? (
                        <div className="image-error">
                          <FileImageOutlined style={{ fontSize: '48px', color: '#ccc' }} />
                          <Text type="secondary" style={{ fontSize: '12px', marginTop: '8px' }}>
                            {t("IMAGE_LOAD_ERROR")}
                          </Text>
                        </div>
                      ) : (
                        <Image
                          src={imageSrc}
                          alt={`Error image ${index + 1}`}
                          className="error-image"
                          onError={(e) => handleImageError(imageFileId, e)}
                          preview={{
                            src: imageSrc,
                            mask: <div className="image-preview-mask">
                              <FileImageOutlined style={{ fontSize: '24px', color: 'white' }} />
                            </div>
                          }}
                        />
                      )}
                      <div className="image-overlay">
                        <Text className="image-number">{index + 1}</Text>
                      </div>
                    </div>
                  );
                })}
              </Image.PreviewGroup>
            </div>
          </div>
        )}

        {videoId && (
          <div className="video-section">
            <div className="media-header">
              <VideoCameraOutlined className="media-icon" />
              <Title level={5} className="media-title">{t("ATTACHED_VIDEO")}</Title>
            </div>
            <div className="video-player">
              {mediaErrors[`video_${videoId._id || videoId}`] ? (
                <div className="video-error">
                  <VideoCameraOutlined style={{ fontSize: '64px', color: '#ccc' }} />
                  <Text type="secondary" style={{ fontSize: '16px', marginTop: '16px' }}>
                    {t("VIDEO_LOAD_ERROR")}
                  </Text>
                </div>
              ) : (
                <video
                  controls
                  width="100%"
                  height="320"
                  src={API.STREAM_MEDIA.format(videoId._id || videoId)}
                  className="error-video"
                  preload="metadata"
                  onError={(e) => handleVideoError(videoId._id || videoId, e)}
                >
                  {t("VIDEO_NOT_SUPPORTED")}
                </video>
              )}
            </div>
          </div>
        )}

        {imageIds.length === 0 && !videoId && (
          <div className="no-media">
            <div className="no-media-icon">
              <FileImageOutlined />
            </div>
            <Text type="secondary" className="no-media-text">
              {t("NO_MEDIA_ATTACHED")}
            </Text>
          </div>
        )}
      </div>
    );
  };

  if (!detailData) {
    return (
      <AntModal
        open={open}
        onCancel={onClose}
        title={t("ERROR_REPORT_DETAILS")}
        width={1000}
        footer={null}
      >
        <Loading active={isLoading} transparent>
          <div style={{ height: 200 }} />
        </Loading>
      </AntModal>
    );
  }

  return (
    <AntModal
      open={open}
      onCancel={onClose}
      title={t("ERROR_REPORT_DETAILS")}
      width={1000}
      footer={null}
      className="error-report-detail-modal"
    >
      <Loading active={isLoading} transparent>
        <div className="error-report-detail-container">

          {/* General Information */}
          <Card title={t("ERROR_REPORT_INFORMATION")} className="info-card">
            <Row gutter={24}>
              <Col span={12}>
                <div className="info-item">
                  <Text strong className="info-label">{t("FULL_NAME")}:</Text>
                  <Text className="info-value">{detailData.userId?.fullName || t("NOT_PROVIDED")}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-item">
                  <Text strong className="info-label">{t("EMAIL")}:</Text>
                  <Text className="info-value">{detailData.userId?.email}</Text>
                </div>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <div className="info-item">
                  <Text strong className="info-label">{t("CREATED_AT")}:</Text>
                  <Text className="info-value">{formatDate(detailData.createdAt)}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div className="info-item">
                  <Text strong className="info-label">{t("ERROR_URL")}:</Text>
                  <a
                    href={detailData.errorUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="info-link"
                  >
                    {detailData.errorUrl}
                  </a>
                </div>
              </Col>
            </Row>

            <Divider />

            <div className="description-section">
              <Text strong className="info-label">{t("DESCRIPTION")}:</Text>
              <Paragraph className="description-text">
                {detailData.description}
              </Paragraph>
            </div>
          </Card>

          {/* Media Content */}
          <Card title={t("MEDIA_CONTENT")} className="info-card">
            {renderMediaContent()}
          </Card>
        </div>
      </Loading>
    </AntModal>
  );
}

export default ErrorReportDetail;
