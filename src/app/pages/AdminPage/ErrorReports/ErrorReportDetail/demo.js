import React, { useState } from "react";
import { <PERSON><PERSON> } from "antd";
import ErrorReportDetail from "./index";

// Demo data for testing simplified view
const mockErrorReport = {
  _id: "67890abcdef12345",
  userId: {
    _id: "user123",
    email: "<EMAIL>",
    fullName: "John Doe"
  },
  description: "The application crashes when trying to upload a large file. This happens consistently with files over 10MB. The error occurs after clicking the upload button and waiting for about 30 seconds. The page becomes unresponsive and shows a white screen.\n\nSteps to reproduce:\n1. Navigate to upload page\n2. Select a file larger than 10MB\n3. Click upload button\n4. Wait for 30 seconds\n5. Page becomes unresponsive",
  errorUrl: "https://app.example.com/upload/documents",
  imageIds: [
    {
      _id: "img1",
      imageFileId: "file123",
      name: "error-screenshot-1.png"
    },
    {
      _id: "img2",
      imageFileId: "file124",
      name: "error-screenshot-2.png"
    },
    {
      _id: "img3",
      imageFileId: "file125",
      name: "error-screenshot-3.png"
    },
    {
      _id: "img4",
      imageFileId: "file126",
      name: "error-screenshot-4.png"
    },
    {
      _id: "img5",
      imageFileId: "file127",
      name: "error-screenshot-5.png"
    }
  ],
  videoId: {
    _id: "video1",
    name: "error-recording.mp4"
  },
  createdAt: "2024-01-15T10:30:00Z"
};

function ErrorReportDetailDemo() {
  const [isOpen, setIsOpen] = useState(false);

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  return (
    <div style={{ padding: "20px" }}>
      <h2>ErrorReportDetail Component Demo (Clean 2-Card Layout)</h2>
      <p>Click the button below to open the clean ErrorReportDetail modal with no duplicate headers:</p>

      <Button type="primary" onClick={handleOpen}>
        Open Error Report Detail
      </Button>

      <ErrorReportDetail
        open={isOpen}
        errorReport={mockErrorReport}
        onClose={handleClose}
      />

      <div style={{ marginTop: "20px", padding: "16px", background: "#f5f5f5", borderRadius: "8px" }}>
        <h3>🎨 UI/UX Nâng Cấp Hoàn Thành:</h3>
        <ul>
          <li>✅ <strong>Nhất quán Admin Design:</strong> Gap 24px, padding 24px chuẩn admin</li>
          <li>✅ <strong>Media Headers Cải tiến:</strong> Background, border-left accent, count badge</li>
          <li>✅ <strong>Image Gallery Thông minh:</strong> Grid layout tối ưu theo số lượng</li>
          <li>✅ <strong>Hover Effects Mượt mà:</strong> Transform, scale, shadow transitions</li>
          <li>✅ <strong>Responsive Video:</strong> Min/max height thay vì fixed height</li>
          <li>✅ <strong>Accessibility:</strong> ARIA labels, keyboard navigation</li>
          <li>✅ <strong>Performance:</strong> Lazy loading với skeleton animation</li>
          <li>✅ <strong>Visual Polish:</strong> Border radius 12px, gradient backgrounds</li>
        </ul>

        <h4>🖼️ Image Gallery Cải Tiến:</h4>
        <ul>
          <li><strong>1 Dòng Layout:</strong> Ảnh nhỏ hơn 5 ảnh luôn cùng trên 1 dòng</li>
          <li><strong>Smart Grid:</strong> 1 ảnh = 200px, 2 ảnh = 400px, 3 ảnh = 600px, 4 ảnh = 800px center</li>
          <li><strong>Media Headers Đơn giản:</strong> Icon và title thẳng hàng, không background phức tạp</li>
          <li><strong>Enhanced Hover:</strong> translateY(-4px) + scale(1.02) + border glow</li>
          <li><strong>Mobile Responsive:</strong> Duy trì 1 dòng cho ảnh ≤4, responsive cho ảnh ≥5</li>
        </ul>

        <h4>Test with 5 Images:</h4>
        <p>This demo includes 5 images to test the full-width gallery layout and even spacing.</p>
      </div>

      <div style={{ marginTop: "20px", padding: "16px", background: "#f0f0f0", borderRadius: "8px" }}>
        <h3>Sample Data Structure:</h3>
        <pre style={{ fontSize: "12px", overflow: "auto", maxHeight: "300px" }}>
          {JSON.stringify(mockErrorReport, null, 2)}
        </pre>
      </div>
    </div>
  );
}

export default ErrorReportDetailDemo;
