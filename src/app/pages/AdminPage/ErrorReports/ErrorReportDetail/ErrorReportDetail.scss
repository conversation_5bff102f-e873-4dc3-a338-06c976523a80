.error-report-detail-modal {
  .ant-modal-content {
    border-radius: 8px;
    box-shadow: var(--shadow-level-2);
  }

  .ant-modal-header {
    border-radius: 8px 8px 0 0;

    .ant-modal-title {
      color: var(--typo-colours-support-white);
      font-weight: 600;
      font-size: 18px;
    }
  }

  .ant-modal-close {
    .ant-modal-close-x {
      color: var(--typo-colours-support-white);
      font-size: 18px;

      &:hover {
        color: var(--typo-colours-support-white);
        opacity: 0.8;
      }
    }
  }

  .ant-modal-body {
    max-height: 75vh;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }
}

.error-report-detail-container {
  display: flex;
  flex-direction: column;
  gap: 24px; // Thay đổi từ 20px thành 24px để nhất quán với admin pages
  font-family: 'Segoe UI', serif;



  // Common card styles - simplified
  .info-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px; // Thay đổi từ 20px thành 24px để nhất quán
    }

    .ant-card-head {
      background-color: var(--background-light-background-1);
      border-bottom: 1px solid var(--lighttheme-content-background-stroke);
      border-radius: 8px 8px 0 0;

      .ant-card-head-title {
        padding: 14px 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);
      }
    }
  }

  // Info items styling
  .info-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-bottom: 16px;

    .info-label {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
    }

    .info-value {
      font-size: 15px;
      color: var(--typo-colours-primary-black);
      font-weight: 500;
    }

    .info-link {
      color: var(--primary-colours-blue);
      text-decoration: none;
      word-break: break-all;
      font-size: 15px;
      transition: all var(--transition-timing);

      &:hover {
        color: var(--primary-colours-blue-navy);
        text-decoration: underline;
      }
    }
  }

  // Description section styling
  .description-section {
    margin-top: 8px;

    .info-label {
      display: block;
      margin-bottom: 12px;
    }

    .description-text {
      margin-bottom: 0;
      line-height: 1.7;
      color: var(--typo-colours-primary-black);
      white-space: pre-wrap;
      word-break: break-word;
      font-size: 15px;
      background-color: var(--background-light-background-2);
      border-radius: 6px;
      padding: 16px;
      border: 1px solid var(--lighttheme-content-background-stroke);
    }
  }

  // Media content styles
  .media-content {
    .media-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    // Ensure perfect icon and text alignment
    .media-header {
      * {
        vertical-align: middle;
      }

      .anticon {
        vertical-align: middle;
      }
    }

    .images-section {
      .media-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        line-height: 1;

        .media-icon {
          color: var(--primary-colours-blue);
          font-size: 16px;
          line-height: 1;
          display: inline-flex;
          align-items: center;
          vertical-align: middle;
          height: 16px;
        }

        .media-title {
          margin: 0 !important;
          color: var(--typo-colours-primary-black);
          font-size: 16px !important;
          font-weight: 600;
          line-height: 1 !important;
          display: flex;
          align-items: center;

          // Override Ant Design Title styles
          &.ant-typography {
            margin-bottom: 0 !important;
            margin-top: 0 !important;
          }
        }
      }

      .images-gallery {
        display: grid;
        gap: 16px;
        width: 100%;
        justify-content: center;
        align-items: center;

        // Ảnh nhỏ hơn 5 ảnh cùng trên 1 dòng - căn giữa theo chiều dọc
        &[data-count="1"] {
          grid-template-columns: 1fr;
          max-width: 200px;
          margin: 0 auto;
          justify-items: center;
        }

        &[data-count="2"] {
          grid-template-columns: repeat(2, 1fr);
          max-width: 400px;
          margin: 0 auto;
          justify-items: center;
        }

        &[data-count="3"] {
          grid-template-columns: repeat(3, 1fr);
          max-width: 600px;
          margin: 0 auto;
          justify-items: center;
        }

        &[data-count="4"] {
          grid-template-columns: repeat(4, 1fr);
          max-width: 800px;
          margin: 0 auto;
          justify-items: center;
        }

        &[data-count="5"], &[data-count="6"], &[data-count="7"], &[data-count="8"] {
          grid-template-columns: repeat(5, 1fr);
          justify-items: center;
        }

        .image-wrapper {
          position: relative;
          border-radius: 12px; // Tăng từ 8px
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          width: 100%;
          aspect-ratio: 1;
          background: linear-gradient(135deg, var(--background-light-background-2), var(--background-light-background-1));
          border: 2px solid transparent;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            transform: translateY(-4px) scale(1.02); // Cải tiến hover effect
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-colours-blue);

            .image-overlay {
              opacity: 1;
              transform: scale(1.1);
            }
          }

          .error-image {
            width: 100% !important;
            height: 100% !important;
            border: 2px solid var(--background-light-background-1);
            transition: all var(--transition-timing);
            cursor: pointer;
            border-radius: 8px;
            object-fit: cover; // Use cover to fill container completely
            display: block;

            &:hover {
              border-color: var(--primary-colours-blue);
            }

            .image-preview-mask {
              display: flex;
              align-items: center;
              justify-content: center;
              background: rgba(0, 0, 0, 0.6);
              border-radius: 4px;
              padding: 8px;
              transition: all var(--transition-timing);

              &:hover {
                background: rgba(0, 0, 0, 0.8);
              }
            }
          }

          .image-overlay {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 50%;
            width: 28px; // Tăng từ 24px
            height: 28px; // Tăng từ 24px
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);

            .image-number {
              font-size: 13px; // Tăng từ 12px
              font-weight: 600;
              color: white;
            }
          }

          .image-error {
            width: 100%;
            height: 100%;
            border: 2px dashed var(--lighttheme-content-background-stroke);
            border-radius: 12px; // Tăng từ 8px
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: var(--background-light-background-2);
            text-align: center;
            padding: 16px;
            box-sizing: border-box;
          }

          // Lazy loading skeleton styles
          .lazy-image-wrapper {
            position: relative;
            width: 100%;
            height: 100%;

            .image-skeleton {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              background: linear-gradient(90deg,
                var(--background-light-background-2) 25%,
                var(--background-light-background-1) 50%,
                var(--background-light-background-2) 75%);
              background-size: 200% 100%;
              animation: shimmer 1.5s infinite;
              border-radius: 12px;
              z-index: 1;

              @keyframes shimmer {
                0% {
                  background-position: -200% 0;
                }
                100% {
                  background-position: 200% 0;
                }
              }
            }
          }
        }
      }
    }

    .video-section {
      .media-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        line-height: 1;

        .media-icon {
          color: var(--primary-colours-blue);
          font-size: 16px;
          line-height: 1;
          display: inline-flex;
          align-items: center;
          vertical-align: middle;
          height: 16px;
        }

        .media-title {
          margin: 0 !important;
          color: var(--typo-colours-primary-black);
          font-size: 16px !important;
          font-weight: 600;
          line-height: 1 !important;
          display: flex;
          align-items: center;

          // Override Ant Design Title styles
          &.ant-typography {
            margin-bottom: 0 !important;
            margin-top: 0 !important;
          }
        }
      }

      .video-player {
        border-radius: 12px; // Tăng từ 8px
        overflow: hidden;
        box-shadow: var(--shadow-level-2); // Tăng từ level-1
        background: #000;
        position: relative;

        .error-video {
          border-radius: 12px; // Tăng từ 8px
          border: none; // Bỏ border
          width: 100%;
          min-height: 300px; // Thay vì fixed height
          max-height: 500px;
          background: #000;

          &:focus {
            outline: 2px solid var(--primary-colours-blue);
            outline-offset: 2px;
          }
        }

        .video-error {
          width: 100%;
          min-height: 300px; // Thay vì fixed height 320px
          border: 2px dashed var(--lighttheme-content-background-stroke);
          border-radius: 12px; // Tăng từ 8px
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          background-color: var(--background-light-background-2);
          text-align: center;
        }

        // Custom video controls styling
        .error-video::-webkit-media-controls-panel {
          background-color: rgba(0, 0, 0, 0.8);
        }

        .error-video::-webkit-media-controls-play-button,
        .error-video::-webkit-media-controls-volume-slider,
        .error-video::-webkit-media-controls-timeline {
          filter: brightness(1.2);
        }
      }
    }
  }

  .no-media {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, var(--background-light-background-2), var(--background-light-background-1));
    border-radius: 12px; // Tăng từ 8px
    border: 2px dashed var(--lighttheme-content-background-stroke);
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--primary-colours-blue-light-1);
      background: linear-gradient(135deg, var(--background-light-background-1), var(--primary-colours-blue-light-1));
    }

    .no-media-icon {
      font-size: 64px; // Tăng từ 48px
      color: var(--typo-colours-support-blue-light);
      margin-bottom: 20px; // Tăng từ 16px
      transition: all 0.3s ease;
    }

    .no-media-text {
      color: var(--typo-colours-support-blue-light);
      font-size: 16px;
      font-style: italic;
      font-weight: 500;
    }
  }
}

// Admin actions card styles
.admin-actions-card {
  .ant-card-head {
    background-color: var(--primary-colours-blue-light-1);
  }


}

// Image preview customization
.ant-image-preview-wrap {
  .ant-image-preview-img {
    max-height: 85vh;
    object-fit: contain;
  }

  .ant-image-preview-operations {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
  }
}

// Animation for loading states
.ant-spin-spinning {
  .error-report-detail-container {
    opacity: 0.7;
    pointer-events: none;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .error-report-detail-modal {
    .ant-modal {
      width: 95% !important;
      margin: 10px;
    }

    .ant-modal-content {
      .ant-modal-body {
        padding: 16px;
      }
    }
  }

  .error-report-detail-container {
    gap: 16px;

    .info-card {
      .ant-row {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .error-report-detail-container {
    .media-content {
      .images-section {
        .images-gallery {
          gap: 12px;

          // Mobile responsive grid - ảnh nhỏ hơn 5 vẫn cùng 1 dòng, căn giữa
          &[data-count="1"] {
            grid-template-columns: 1fr;
            max-width: 150px;
            margin: 0 auto;
            justify-items: center;
          }

          &[data-count="2"] {
            grid-template-columns: repeat(2, 1fr);
            max-width: 300px;
            margin: 0 auto;
            justify-items: center;
          }

          &[data-count="3"] {
            grid-template-columns: repeat(3, 1fr);
            max-width: 450px;
            margin: 0 auto;
            justify-items: center;
          }

          &[data-count="4"] {
            grid-template-columns: repeat(4, 1fr);
            justify-items: center;
          }

          &[data-count="5"], &[data-count="6"], &[data-count="7"], &[data-count="8"] {
            grid-template-columns: repeat(5, 1fr);
            justify-items: center;
          }

          .image-wrapper {
            .error-image {
              width: 100% !important;
              height: 100% !important;
            }

            .image-error {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .video-section {
        .video-player {
          .error-video {
            min-height: 250px; // Thay vì fixed height 200px
          }

          .video-error {
            min-height: 250px; // Đồng bộ với video
          }
        }
      }
    }
  }
}

// Hover effects and transitions
.error-report-detail-container {
  .ant-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-1px);
    }
  }

  .ant-divider {
    margin: 20px 0;
    border-color: var(--lighttheme-content-background-stroke);
  }

  // Custom tag styles
  .ant-tag {
    border-radius: 4px;
    font-weight: 500;
    padding: 4px 8px;
    border: none;
  }

  // Link styles
  a {
    transition: all var(--transition-timing);

    &:hover {
      text-decoration: none;
    }
  }
}

// Small mobile optimization
@media (max-width: 480px) {
  .error-report-detail-container {
    .media-content {
      .images-section {
        .images-gallery {
          gap: 8px;

          // Small mobile: ảnh nhỏ hơn 5 vẫn cùng 1 dòng, căn giữa
          &[data-count="1"] {
            grid-template-columns: 1fr;
            max-width: 120px;
            margin: 0 auto;
            justify-items: center;
          }

          &[data-count="2"] {
            grid-template-columns: repeat(2, 1fr);
            max-width: 240px;
            margin: 0 auto;
            justify-items: center;
          }

          &[data-count="3"] {
            grid-template-columns: repeat(3, 1fr);
            max-width: 360px;
            margin: 0 auto;
            justify-items: center;
          }

          &[data-count="4"] {
            grid-template-columns: repeat(4, 1fr);
            justify-items: center;
          }

          &[data-count="5"], &[data-count="6"], &[data-count="7"], &[data-count="8"] {
            grid-template-columns: repeat(5, 1fr);
            justify-items: center;
          }
        }
      }

      .video-section {
        .video-player {
          .error-video {
            min-height: 200px; // Responsive cho small mobile
          }

          .video-error {
            min-height: 200px;
          }
        }
      }
    }
  }
}
